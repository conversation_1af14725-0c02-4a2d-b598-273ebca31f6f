[group:ops_management_htm]
programs=gunicorn_ops_management_htm,celery_worker_ops_management_htm,celery_beat_ops_management_htm

[program:gunicorn_ops_management_htm]
command=bash -c "source /root/miniconda3/bin/activate env_py310_htm && gunicorn ops_management.wsgi:application -c gunicorn_config.py"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/ops_management_htm  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/ops_management_htm/logs/gunicorn.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/ops_management_htm/logs/gunicorn.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=TENCENTCLOUD_SECRET_ID="AKIDBE4lRNBoETYWJII7nJeokU1MXXO3Xnqg",TENCENTCLOUD_SECRET_KEY="gBUbWEk3961kIfpmW3PZWL1Vt77vWuAQ"

[program:celery_worker_ops_management_htm]
command=bash -c "source /root/miniconda3/bin/activate env_py310_htm && celery -A ops_management worker --loglevel=info"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/ops_management_htm  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/ops_management_htm/logs/worker.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/ops_management_htm/logs/worker.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=TENCENTCLOUD_SECRET_ID="AKIDBE4lRNBoETYWJII7nJeokU1MXXO3Xnqg",TENCENTCLOUD_SECRET_KEY="gBUbWEk3961kIfpmW3PZWL1Vt77vWuAQ"

[program:celery_beat_ops_management_htm]
command=bash -c "source /root/miniconda3/bin/activate env_py310_htm && celery -A ops_management beat --loglevel=info"  # 指定要运行的程序的命令
directory=/mnt/d/pythonProject/ops_management_htm  # 指定程序的工作目录
autostart=false  # 是否随 Supervisor 启动自动启动该进程
autorestart=true  # 是否在进程退出时自动重启
stderr_logfile=/mnt/d/pythonProject/ops_management_htm/logs/beat.err.log  # 错误日志文件路径
stdout_logfile=/mnt/d/pythonProject/ops_management_htm/logs/beat.out.log  # 输出日志文件路径
user=root  # 运行进程的用户
environment=TENCENTCLOUD_SECRET_ID="AKIDBE4lRNBoETYWJII7nJeokU1MXXO3Xnqg",TENCENTCLOUD_SECRET_KEY="gBUbWEk3961kIfpmW3PZWL1Vt77vWuAQ"
