# 加密与安全
cryptography==43.0.0  # 提供加密功能的库，支持加密协议和算法，如 SSL/TLS、对称加密和非对称加密。
gmssl==3.2.2  # 提供国密算法支持的加密库，适用于中国的商用密码标准。
pycryptodome==3.20.0  # 提供加密算法的库，支持对称加密、非对称加密和散列函数。

# 数据处理、图像与文档处理
chinese_calendar  # 用于处理中国农历、节假日及工作日信息的库。
docxtpl==0.16.7  # 用于生成 Word 文档的模板库，支持动态数据填充。
openpyxl==3.1.2  # 用于读取和写入 Excel 文件的库，支持 xlsx 格式。
pandas==2.2.2  # 强大的数据分析和数据操作库，提供 DataFrame 数据结构。
pillow==10.4.0  # 图像处理库，支持各种图像格式的打开、操作和保存。
PyPDF2==3.0.1  # 用于操作 PDF 文件的库，支持合并、拆分、旋转和加密 PDF。
python-dateutil==2.8.2  # 提供强大的日期处理功能，支持解析、计算、时区转换等操作。
reportlab==4.2.2  # 用于生成 PDF 文档的库，支持复杂的页面布局、图表和表格。
xlrd==2.0.1  # 用于读取 Excel 文件的库，支持 xls 格式（旧版 Excel）。

# 网络与HTTP操作
requests==2.32.3  # 简单易用的 HTTP 库，用于发送 HTTP 请求。

# 并发与异步任务
celery==5.4.0  # 分布式任务队列，用于处理异步任务和调度定时任务。
concurrent-log-handler==0.9.25  # 支持多进程写日志文件的处理程序，防止日志文件损坏。
gevent==23.9.1  # 基于协程的 Python 并发库，支持事件循环和网络通信。
gunicorn==20.1.0  # Python WSGI HTTP 服务器，用于部署 Django 等 Web 应用。

# 性能优化与编译
Cython  # 优化 Python 代码性能的编译器，能够将 Python 代码转换为 C 语言，提高执行速度。

# Django框架及其扩展
django==4.1.13  # 高级 Python Web 框架，鼓励快速开发和简洁、实用的设计。（由于达梦驱动仅支持到django4.1）
django-cacheops  # 为 Django 提供自动缓存机制，支持 Redis 后端的缓存操作。
django-celery-results==2.5.1  # Django 与 Celery 的集成，提供任务结果的存储后端。
django-debug-toolbar==4.2.0  # Django 开发调试工具栏，提供对 Django 项目的性能监控和调试信息。
django-filter==23.5  # 为 Django 提供过滤器功能，简化复杂查询集的构建。（23.5最后支持django4.1）
django-ipware  # 用于 Django 项目中获取客户端的 IP 地址，用于IP白名单权限验证
django-oauth-toolkit==2.4.0  # Django 的 OAuth 2.0 提供工具包，便于实现 OAuth 认证。
django-redis==5.3.0  # 将 Django 的缓存和会话存储在 Redis 中。
djangorestframework==3.14.0  # Django REST 框架，用于构建 Web API。（3.14.0最后支持django4.1）
drf-chunked-upload==0.6.0  # 适用于 Django REST 框架的大文件分块上传库。
drf-spectacular==0.26.4  # 自动生成基于 OpenAPI 规范的 API 文档，适用于 Django REST 框架。

# 数据库连接与操作
mysqlclient==2.2.7  # MySQL 数据库驱动，提供与 MySQL 数据库的连接和操作接口。

# 其他
id-validator # 身份证号码验证库
