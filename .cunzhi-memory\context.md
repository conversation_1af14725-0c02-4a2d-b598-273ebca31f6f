# 项目上下文信息

- 已创建债权人测试数据生成脚本 generate_creditor_data.py，包含完整的中文本地化数据生成逻辑，无需外部依赖，可生成20条符合CreditorBasicInfo模型要求的测试数据
- 新Django应用命名决策：outbound_communication - 负责外呼管理功能，包含语音外呼和短信发送两个核心模块，符合项目命名规范和架构风格
- Django应用outbound_communication创建完成：应用中文名称调整为"对外通信"，已完成基础结构搭建、settings和urls配置，包含views和serializers模块化目录结构
- outbound_communication应用完整开发完成：创建VoiceCallRecord和SmsRecord模型、对应的List序列化器、只读ViewSet视图集，已注册URL路由，提供完整的查询、过滤、搜索功能
- SmsRecord模型字段修改完成：删除template_id字段，将batch_id重命名为task_batch_id，同步更新了序列化器字段列表、视图集过滤字段和文档字符串，保持了代码风格和架构一致性
- AssetPackageFieldMapping模型新增mapped_debtor_field_config字段：CharField类型，使用DEBTOR_FIELD_CHOICES选项，包含债务人模型的8个字段选项，字段可选(blank=True, null=True)，需要同步更新相关序列化器支持新字段的序列化和反序列化操作
- 需要实现Excel数据自动导入功能：在AssetPackageManagementUpdateSerializer中添加_import_debtor_data_from_excel方法，当资产包状态为available且存在id_number字段映射时，自动将Excel数据导入到DebtorBasicInfo模型，使用pandas读取Excel，通过证件号码去重，集成到update方法的数据验证流程之后
- 需要在AssetPackageManagementViewSet中新增debtor_field_choices接口：GET方法，返回AssetPackageFieldMapping.DEBTOR_FIELD_CHOICES选择项，使用AjaxResult.success()包装，数据格式为value/label结构，用于前端字段映射配置的下拉选择
- Django视图重构项目：将mediation_case_views.py中的5个视图类拆分到独立文件中，包括MediationCaseByDebtorView、MediationContentView、MediationPlanConfigView、MediationCaseListAPIView、MediationAgreementPDFView
- 调解管理系统URL路径参数重构已完成：将5个视图接口从使用id参数改为使用case_number参数，移除双重校验逻辑，提高系统安全性。涉及文件：urls.py, mediation_case_detail_view.py, mediation_case_pdf_view.py, mediation_case_view_set.py
