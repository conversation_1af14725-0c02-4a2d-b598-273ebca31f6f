# 开发规范和规则
- Django ViewSet禁用PATCH方法要求：通过重写partial_update方法返回405 Method Not Allowed响应，保持PUT请求可用，添加中文注释说明禁用原因，遵循项目代码规范
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- MyPermission权限拦截规则：对用户名以"wx_"开头的用户直接拒绝访问（返回False），在权限检查方法中添加前缀检查逻辑
- AURA-X协议强制交互规则：AI在完成任何代码修改后必须通过"寸止"MCP工具向用户确认结果，严禁自作主张结束任务，所有决策权完全掌握在用户手中
- AURA-X协议合规性检查标准流程：1)必须先调用记忆工具加载项目信息 2)声明任务复杂度等级(Level 1-4)和执行模式 3)所有用户交互必须通过寸止MCP进行 4)需要最新技术信息时调用context7-mcp 5)严格遵循"AI绝不自作主张"原则- DebtorBasicInfo模型id_number字段唯一性约束：已为id_number字段添加unique=True约束，同步更新了相关序列化器和视图的docstring说明唯一性特征，需要运行数据库迁移命令生效
- AURA-X协议任务分析：Django项目SPECTACULAR_SETTINGS配置优化任务，实现基于URL路由的多级菜单自动生成功能，任务复杂度评定为Level 2（标准任务），推荐执行模式为LITE-CYCLE，需要通过preprocessing hook实现URL路径解析和标签分组
- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，不要编译，不要运行，用户自己处理这些操作
- 文件下载安全重构完成：为AssetPackageManagementFile和MediationCaseFile模型添加secure_token UUID字段，重构FileDownloadView使用UUID标识符替代文件路径，创建FileSecurityHelper工具类，更新序列化器包含安全下载链接，URL路由改为/user/files/download/<uuid:secure_token>/，实现完整的文件下载安全防护机制
- 调解案例功能修改规则：1)MediationCase模型新增confirmed_mediation_config和confirmed_plan_config两个JSONField字段 2)update_mediation_plan接口增加业务逻辑：若调解案件已关联调解方案则不支持更新 3)用户确认调解方案时自动存储资产包的mediation_config和调解方案的plan_config到对应的confirmed字段 4)数据库迁移文件由用户手动执行命令创建
- MediationCase模型优化：1)移除mediation_agreement FileField字段，不再直接存储PDF文件 2)新增signature_date DateTimeField字段用于记录调解协议签署时间 3)PDF文件统一存储到MediationCaseFile模型并关联到attachments字段 4)PDF生成时使用signature_date字段显示签署日期，如未设置则自动设为当前时间 5)序列化器移除mediation_agreement相关字段，新增signature_date字段
- PDF中文字体支持：1)在MediationAgreementPDFView中添加_register_chinese_font方法，支持跨平台中文字体注册 2)字体优先级：微软雅黑>黑体>宋体>ReportLab内置中文字体>Helvetica备选 3)支持Windows/macOS/Linux系统的中文字体路径 4)所有ParagraphStyle都使用注册的中文字体，确保标题、正文、表格等内容正确显示中文 5)添加完整的异常处理和日志记录
- PDF签名信息布局优化：1)将签署人信息和签署日期分成两行显示，第一行显示签署人（电子签名图片或文本），第二行显示签署日期 2)签署日期逻辑：signature_date不为NULL时显示"签署日期：YYYY年MM月DD日"，为NULL时显示"签署日期：[未签署]" 3)表格布局：使用两行Table结构，右对齐，设置合适的边距和行间距，确保紧贴PDF右下角 4)保持电子签名图片加载逻辑不变
- PDF文件清理机制：1)在MediationAgreementPDFView中添加_cleanup_existing_pdf_files方法，在保存新PDF前清理同名旧文件 2)清理范围：从attachments关系移除、删除MediationCaseFile数据库记录、删除物理文件 3)文件名匹配："调解协议_{case_number}.pdf" 4)完整异常处理：单个文件删除失败不影响其他文件清理，清理失败不影响新文件保存 5)详细日志记录：记录清理过程和结果
- 调解视图URL路径参数重构完成：将MediationContentView和MediationPlanConfigView的URL路径参数从<int:mediation_case_id>修改为<str:case_number>，同步更新视图方法参数接收、数据库查询逻辑、序列化器字段验证和相关日志记录，统一使用case_number参数进行调解案件查询，提高系统一致性
